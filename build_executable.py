#!/usr/bin/env python3
"""
Build script to create a standalone Windows executable for Printify Image Uploader
"""

import os
import subprocess
import sys
import shutil

def check_pyinstaller():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        print("✓ PyInstaller is installed")
        return True
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return False

def build_executable():
    """Build the standalone executable"""
    print("\n🚀 Building Printify Image Uploader executable...")
    
    # Check required files
    required_files = [
        "PrintifyUploader.py",
        "USER_GUIDE.md", 
        "QUICK_REFERENCE.md",
        "Generate New Token.jpg",
        "Copy To Clipboard.jpg",
        "app_icon.ico"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    print("✓ All required files found")
    
    # Build command
    cmd = [
        "pyinstaller",
        "--onefile",                    # Single executable file
        "--windowed",                   # No console window
        "--icon=app_icon.ico",          # Application icon
        "--name=Printify Image Uploader", # Executable name
        "--add-data=USER_GUIDE.md;.",   # Include user guide
        "--add-data=QUICK_REFERENCE.md;.", # Include quick reference
        "--add-data=Generate New Token.jpg;.", # Include images
        "--add-data=Copy To Clipboard.jpg;.",
        "--add-data=app_icon.ico;.",    # Include icon
        "--clean",                      # Clean cache
        "PrintifyUploader.py"           # Main script
    ]
    
    try:
        print(f"Running: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        print("\n✅ Build completed successfully!")
        
        # Check if executable was created
        exe_path = os.path.join("dist", "Printify Image Uploader.exe")
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📦 Executable created: {exe_path}")
            print(f"📏 File size: {size_mb:.1f} MB")
            
            # Create distribution folder
            dist_folder = "Printify_Uploader_Standalone"
            if os.path.exists(dist_folder):
                shutil.rmtree(dist_folder)
            os.makedirs(dist_folder)
            
            # Copy executable and documentation
            shutil.copy2(exe_path, dist_folder)
            shutil.copy2("README.md", dist_folder)
            
            print(f"\n📁 Distribution package created in: {dist_folder}/")
            print("🎉 Ready to distribute!")
            
            return True
        else:
            print("❌ Executable not found after build")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False

def main():
    """Main build process"""
    print("🔨 Printify Image Uploader - Executable Builder")
    print("=" * 50)
    
    # Check PyInstaller
    if not check_pyinstaller():
        return
    
    # Build executable
    if build_executable():
        print("\n✅ Build process completed successfully!")
        print("\nNext steps:")
        print("1. Test the executable in the dist/ folder")
        print("2. Distribute the Printify_Uploader_Standalone/ folder")
        print("3. Users can run the .exe file without Python installed")
    else:
        print("\n❌ Build process failed")

if __name__ == "__main__":
    main()
