# Printify Image Uploader - Quick Reference

## 🚀 Quick Start
1. **Settings** → **Manage API Token** → Enter your Printify API token → **Save Token**
2. **Browse...** → Select folder with images
3. Select images using checkboxes or **✅ Select All**
4. Click **Upload Selected Images**

## 📁 Supported File Types
- PNG (.png)
- JPEG (.jpg, .jpeg)
- SVG (.svg)

## 🎯 Selection Controls
| Icon | Action |
|------|--------|
| ✅ Select All | Click to select all images |
| ❌ Unselect All | Click to unselect all images |
| ✅ (in rows) | Selected for upload |
| Empty checkbox | Not selected |

## ⚙️ Upload Modes
- **✅ Upload in Order**: Sequential upload (safer, slower)
- **❌ Upload in Order**: Parallel upload (faster, less reliable)

## 📊 Status Indicators
| Icon | Meaning |
|------|---------|
| ⏳ Uploading... | Currently uploading |
| ✅ Uploaded | Successfully uploaded |
| ❌ Failed (XXX) | Failed with error code |
| ❌ Error: ... | Failed with error message |
| ⏹ Stopped | Upload was stopped |

## 🔐 Token Status (Bottom Bar)
| Status | Meaning |
|--------|---------|
| ✓ API Token Ready (blue) | Ready to upload |
| ✓ API Token Saved (green) | Token just saved |
| ⚠ No API Token (orange) | Need to set token |

## 🔧 Common Actions
- **Stop Upload**: Click upload button during upload
- **Clear Statuses**: Start a new upload
- **Update Token**: Settings → Manage API Token
- **Get Help**: Help → User Guide

## ⚡ Keyboard Tips
- **Space**: Toggle selection on focused item
- **Ctrl+A**: Select all (when list is focused)
- **Enter**: Start upload (when upload button is focused)

## 🆘 Troubleshooting
| Problem | Solution |
|---------|----------|
| No token warning | Settings → Manage API Token |
| Failed (401) | Invalid token - update in settings |
| Failed (413) | File too large - reduce image size |
| No images shown | Check folder has supported formats |

## 📞 Quick Help
- **User Guide**: Help → User Guide (full documentation)
- **About**: Help → About (version info)
- **Token Security**: Tokens are encrypted and auto-saved

---
*For complete documentation, see USER_GUIDE.md or Help → User Guide*
