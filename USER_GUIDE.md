# Printify Image Uploader - User Guide

## Table of Contents
1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [Setting Up Your API Token](#setting-up-your-api-token)
4. [Using the Application](#using-the-application)
5. [Upload Options](#upload-options)
6. [Understanding Status Indicators](#understanding-status-indicators)
7. [Troubleshooting](#troubleshooting)
8. [Security Features](#security-features)

## Overview

The Printify Image Uploader is a desktop application that allows you to easily upload multiple images to your Printify account. The application provides a user-friendly interface for selecting, managing, and uploading images with real-time status tracking.

### Key Features
- Bulk image upload to Printify
- Secure API token management with encryption
- Individual image selection control
- Two upload modes: Sequential and Parallel
- Real-time upload progress tracking
- Support for PNG, JPG, JPEG, and SVG formats
- Automatic token saving and loading

## Getting Started

### System Requirements
- Windows operating system
- Python 3.7 or higher
- Internet connection for uploading to Printify

### Installation
1. Ensure Python is installed on your system
2. Install required dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Run the application:
   ```
   python PrintifyUploader.py
   ```

### First Launch
When you first launch the application, you'll see:
- A full-screen window with the main interface
- An orange warning in the status bar: "⚠ No API Token - Use Settings > Manage API Token"
- All upload functionality will be disabled until you set up your API token

## Setting Up Your API Token

### Getting Your Printify API Token
1. Log in to your Printify account
2. Go to your account settings
3. Navigate to the API section
4. Generate or copy your API token

### Adding Your Token to the Application
1. Click **Settings** in the menu bar
2. Select **Manage API Token**
3. In the dialog that opens:
   - Enter your Printify API token in the text field
   - Optionally check "Show token" to view the token as you type
   - Click **Save Token**
4. You'll see a success message and the dialog will close
5. The status bar will show "✓ API Token Ready" in blue

### Token Security
- Your API token is encrypted using machine-specific data
- The token is automatically saved and loaded when you restart the application
- You can clear the saved token at any time using the "Clear Token" button

## Using the Application

### Loading Images

#### Step 1: Select a Folder
1. Click the **Browse...** button next to the "Folder:" field
2. Navigate to the folder containing your images
3. Select the folder and click "Select Folder"

#### Step 2: Review Loaded Images
- The application will automatically scan the folder for supported image formats
- Supported formats: `.png`, `.jpg`, `.jpeg`, `.svg`
- Images will appear in the main table with three columns:
  - **Select**: Checkbox column for choosing which images to upload
  - **Image**: Full path to the image file
  - **Status**: Upload status (initially empty)

### Selecting Images for Upload

#### Individual Selection
- Click on any checkbox in the "Select" column to toggle individual images
- ✅ = Selected for upload
- Empty = Not selected

#### Select All / Unselect All
- Click the column header to select or unselect all images at once
- **✅ Select All**: Clicking will select all images
- **❌ Unselect All**: Clicking will unselect all images
- The header text changes dynamically based on the current state

### Upload Process

#### Starting an Upload
1. Ensure you have selected the images you want to upload
2. Choose your upload mode (see Upload Options below)
3. Click the **Upload Selected Images** button

#### During Upload
- The button changes to **Stop Upload** with a different style
- Each image shows its current status in the Status column
- You can stop the upload at any time by clicking **Stop Upload**

#### Upload Completion
- The button returns to **Upload Selected Images**
- Final status is shown for each image
- Previous upload statuses are cleared when starting a new upload

## Upload Options

### Upload in Order (Default)
- **Checkbox**: "Upload in Order" (checked by default)
- **Behavior**: Images are uploaded one at a time in the order they appear
- **Advantages**: 
  - More reliable for large batches
  - Easier to track progress
  - Less likely to overwhelm the API
- **Use when**: You have many images or prefer sequential processing

### Parallel Upload
- **Checkbox**: "Upload in Order" (unchecked)
- **Behavior**: Multiple images are uploaded simultaneously
- **Advantages**: 
  - Faster overall upload time
  - Better for smaller batches
- **Use when**: You have fewer images and want maximum speed

## Understanding Status Indicators

### Upload Status Icons
- **⏳ Uploading...**: Image is currently being uploaded
- **✅ Uploaded**: Image was successfully uploaded to Printify
- **❌ Failed (XXX)**: Upload failed with HTTP status code
- **❌ Error: [message]**: Upload failed due to an error
- **⏹ Stopped**: Upload was manually stopped

### Token Status Indicators
Located in the bottom status bar:
- **✓ API Token Ready** (blue): Token is loaded and ready to use
- **✓ API Token Saved** (green): Token was just saved successfully
- **⚠ No API Token** (orange): No token configured - uploads disabled

### Selection Status
- **✅ Select All**: Click to select all images
- **❌ Unselect All**: Click to unselect all images

## Troubleshooting

### Common Issues

#### "Missing API Token" Warning
- **Problem**: No API token configured
- **Solution**: Go to Settings > Manage API Token and enter your token

#### Upload Fails with "Failed (401)"
- **Problem**: Invalid or expired API token
- **Solution**: Update your API token in Settings > Manage API Token

#### Upload Fails with "Failed (413)"
- **Problem**: Image file too large
- **Solution**: Reduce image file size or check Printify's size limits

#### No Images Appear After Selecting Folder
- **Problem**: Folder contains no supported image formats
- **Solution**: Ensure folder contains .png, .jpg, .jpeg, or .svg files

#### Application Won't Start
- **Problem**: Missing dependencies
- **Solution**: Run `pip install -r requirements.txt`

### Getting Help
If you encounter issues not covered here:
1. Check the Status column for specific error messages
2. Verify your internet connection
3. Confirm your API token is valid in your Printify account
4. Try uploading a single image first to test connectivity

## Security Features

### Token Encryption
- API tokens are encrypted using machine-specific data
- Tokens are stored in `.printify_config.dat` file
- Encryption provides protection against casual token theft

### Token Management
- Tokens can be easily updated or cleared
- Show/hide functionality for secure token entry
- Automatic loading on application startup

### Best Practices
- Keep your API token confidential
- Regularly update your token if needed
- Use the "Clear Token" feature if sharing the computer
- Don't share the `.printify_config.dat` file

---

**Note**: This application is designed for use with the Printify API. Ensure you have appropriate permissions and follow Printify's terms of service when uploading images.
