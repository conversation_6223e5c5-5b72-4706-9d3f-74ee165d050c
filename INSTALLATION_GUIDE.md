# Printify Image Uploader - Installation Guide

## 🚀 Easy Installation (No Technical Knowledge Required)

### For End Users - Standalone Executable

#### Step 1: Download
1. Download the latest release zip file from the releases page
2. The file will be named something like `Printify_Uploader_v1.0.zip`

#### Step 2: Extract
1. Right-click the downloaded zip file
2. Select "Extract All..." or "Extract Here"
3. Choose a location on your computer (e.g., Desktop, Documents)

#### Step 3: Run
1. Open the extracted folder
2. Double-click `Printify Image Uploader.exe`
3. The application will start immediately!

### ✅ That's It!
- **No Python installation required**
- **No additional software needed**
- **No complex setup process**
- **Works on any Windows computer**

## 📋 System Requirements

### Minimum Requirements:
- **Operating System**: Windows 7 or newer (Windows 10/11 recommended)
- **RAM**: 2 GB minimum (4 GB recommended)
- **Storage**: 50 MB free space
- **Internet**: Required for uploading to Printify

### Supported Systems:
- ✅ Windows 10/11 (fully tested)
- ✅ Windows 8/8.1 (compatible)
- ✅ Windows 7 (compatible, but not recommended)

## 🔧 First Time Setup

### 1. Get Your Printify API Token
1. Log in to your Printify account
2. Go to Account → Connections (https://printify.com/app/account/api)
3. Click "Generate" button
4. Enter a token name (e.g., "Image Uploader")
5. Select "Custom scopes"
6. Choose: uploads.read and uploads.write
7. Copy the generated token

### 2. Configure the Application
1. Open Printify Image Uploader
2. Go to Settings → Manage API Token
3. Paste your API token
4. Click "Save Token"
5. You're ready to upload!

## 📁 File Organization

### Recommended Folder Structure:
```
Desktop/
└── Printify Image Uploader/
    ├── Printify Image Uploader.exe    # Main application
    └── README.md                      # Basic instructions
```

### Your Images:
- Keep your images in organized folders
- Supported formats: PNG, JPG, JPEG, SVG
- Check file size limits (PNG/JPG: 100 MB, SVG: 20 MB)

## 🆘 Troubleshooting

### Application Won't Start
**Problem**: Double-clicking the .exe does nothing
**Solutions**:
1. Right-click the .exe → "Run as administrator"
2. Check Windows Defender/antivirus isn't blocking it
3. Re-download the application
4. Restart your computer

### Windows Security Warning
**Problem**: "Windows protected your PC" message
**Solution**:
1. Click "More info"
2. Click "Run anyway"
3. This is normal for new applications

### Antivirus False Positive
**Problem**: Antivirus software blocks the application
**Solution**:
1. Add the application to your antivirus whitelist
2. This is common with PyInstaller-built applications
3. The application is safe - it only uploads images to Printify

### Slow Startup
**Problem**: Application takes a long time to start
**Explanation**:
- First startup may take 5-10 seconds
- Subsequent startups will be faster
- This is normal for standalone executables

## 🔄 Updates

### How to Update:
1. Download the new version
2. Extract to a new folder (or replace the old one)
3. Your API token settings will be preserved
4. No need to reconfigure

### Automatic Updates:
- Currently not available
- Check for new releases manually
- Future versions may include auto-update

## 🗑️ Uninstallation

### To Remove the Application:
1. Close the application if running
2. Delete the application folder
3. Optionally delete the `.printify_config.dat` file from the same folder

### Clean Uninstall:
- No registry entries are created
- No system files are modified
- Simply delete the folder to completely remove

## 📞 Support

### Getting Help:
1. **Built-in Help**: Help → User Guide (comprehensive documentation)
2. **Quick Reference**: Help → Quick Reference (common tasks)
3. **API Token Help**: Help → How to Get API Token (with images)

### Common Questions:
- **Q**: Do I need Python installed?
- **A**: No! The executable includes everything needed.

- **Q**: Can I run this on multiple computers?
- **A**: Yes! Just copy the folder to each computer.

- **Q**: Is my API token safe?
- **A**: Yes! It's encrypted and stored securely on your computer.

- **Q**: Can I use this offline?
- **A**: You can browse and select images offline, but need internet to upload.

## 🎉 You're Ready!

Your Printify Image Uploader is now installed and ready to use. Enjoy easy bulk uploading of your images to Printify!

---

**Need more help?** Use the built-in help system: Help → User Guide
