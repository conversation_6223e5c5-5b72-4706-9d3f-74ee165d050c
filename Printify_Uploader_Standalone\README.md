# Printify Image Uploader

A desktop application for bulk uploading images to Printify with secure API token management and real-time progress tracking.

![Application Screenshot](app_icon.ico)

## Features

- 🚀 **Bulk Upload**: Upload multiple images simultaneously or sequentially
- 🔐 **Secure Token Management**: Encrypted API token storage with auto-save/load
- 📊 **Real-time Progress**: Live status updates for each upload
- 🎯 **Flexible Selection**: Individual or bulk image selection controls
- 📁 **Multi-format Support**: PNG, JPG, JPEG, and SVG files
- ⚡ **Two Upload Modes**: Sequential (reliable) or Parallel (fast)
- 🛑 **Upload Control**: Start, stop, and resume uploads
- 📖 **Built-in Help**: Comprehensive user guide and quick reference

## Quick Start

### Option 1: Standalone Executable (Recommended for End Users)

1. **Download** the latest release from the releases page
2. **Extract** the zip file to your desired location
3. **Run** `Printify Image Uploader.exe` - no Python installation required!

### Option 2: Run from Source (For Developers)

1. **Install Dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**:

   ```bash
   python PrintifyUploader.py
   ```

### Option 3: Build Your Own Executable

1. **Install PyInstaller**:

   ```bash
   pip install pyinstaller
   ```

2. **Run the build script**:

   ```bash
   python build_executable.py
   ```

   Or simply double-click `build.bat` on Windows

3. **Set Up API Token**:

   - Go to **Settings** → **Manage API Token**
   - Enter your Printify API token (see [Getting Your API Token](#getting-your-api-token) below)
   - Click **Save Token**

4. **Upload Images**:
   - Click **Browse...** to select a folder with images
   - Select images using checkboxes or **✅ Select All**
   - Click **Upload Selected Images**

## Documentation

### 📖 Complete Guide

- **[USER_GUIDE.md](USER_GUIDE.md)** - Comprehensive documentation with detailed instructions, troubleshooting, and security information

### ⚡ Quick Reference

- **[QUICK_REFERENCE.md](QUICK_REFERENCE.md)** - Printable quick reference card with icons, shortcuts, and common solutions

### 🆘 In-App Help

- **Help** → **User Guide** - Opens the complete user guide
- **Help** → **About** - Application information and feature summary

## Getting Your API Token

To use this application, you'll need a Printify API token:

1. **Log in** to your Printify account
2. **Navigate** to **Account** → **Connections** at the bottom left, or go directly to: https://printify.com/app/account/api
3. **Click "Generate"** button to create an API token
4. **Enter a Token name** (e.g., "Image Uploader")
5. **Select "Custom scopes"** in "What sort of scopes do you need?"
6. **In "Scopes" section, select:**
   - uploads.read
   - uploads.write

### Step 1: Generate New Token

![Generate New Token](Generate%20New%20Token.jpg)

### Step 2: Copy Token to Clipboard

![Copy To Clipboard](Copy%20To%20Clipboard.jpg)

## System Requirements

- **Operating System**: Windows (primary), macOS, Linux
- **Python**: 3.7 or higher
- **Internet**: Required for uploading to Printify API
- **Dependencies**: Listed in `requirements.txt`

## File Structure

```
PrintifyUploader/
├── PrintifyUploader.py         # Main application
├── requirements.txt            # Python dependencies
├── app_icon.ico               # Application icon
├── USER_GUIDE.md              # Complete documentation
├── QUICK_REFERENCE.md         # Quick reference card
├── README.md                  # This file
├── Generate New Token.jpg     # API token setup guide image
├── Copy To Clipboard.jpg      # API token setup guide image
├── build_executable.py        # Build script for creating executable
├── build.bat                  # Windows batch file for easy building
├── PrintifyUploader.spec      # PyInstaller configuration file
├── dist/                      # Generated executable (after build)
├── build/                     # Build cache (after build)
└── .printify_config.dat       # Encrypted token storage (auto-created)
```

## Security Features

- **Token Encryption**: API tokens are encrypted using machine-specific data
- **Secure Storage**: Tokens stored in encrypted `.printify_config.dat` file
- **Auto-loading**: Tokens automatically loaded on application startup
- **Easy Management**: Simple token update and clearing functionality

## Supported Image Formats & Printify Requirements

- **PNG** (.png): Maximum 100 MiB, Maximum resolution 30,000 × 30,000 px
- **JPEG** (.jpg, .jpeg): Maximum 100 MiB, Maximum resolution 30,000 × 30,000 px
- **SVG** (.svg): Maximum 20 MiB, Maximum resolution 30,000 × 30,000 px

## Upload Modes

### Sequential Upload (Default)

- ✅ **Upload in Order** checked
- Images uploaded one at a time
- More reliable for large batches
- Easier progress tracking

### Parallel Upload

- ❌ **Upload in Order** unchecked
- Multiple images uploaded simultaneously
- Faster overall completion
- Better for smaller batches

## Status Indicators

| Icon | Status       | Description                             |
| ---- | ------------ | --------------------------------------- |
| ⏳   | Uploading... | Currently being uploaded                |
| ✅   | Uploaded     | Successfully uploaded                   |
| ❌   | Failed       | Upload failed (with error code/message) |
| ⏹    | Stopped      | Upload manually stopped                 |

## Getting Help

1. **Built-in Help**: Use **Help** → **User Guide** in the application
2. **Quick Reference**: See [QUICK_REFERENCE.md](QUICK_REFERENCE.md) for common tasks
3. **Full Documentation**: Read [USER_GUIDE.md](USER_GUIDE.md) for complete instructions
4. **Troubleshooting**: Check the Status column for specific error messages

## License

This application is designed for use with the Printify API. Please ensure you have appropriate permissions and follow Printify's terms of service when uploading images.

## Version

**v1.0** - Initial release with full upload functionality, secure token management, and comprehensive documentation.

---

**Need help?** Check **Help** → **User Guide** in the application or see [USER_GUIDE.md](USER_GUIDE.md) for detailed instructions.
